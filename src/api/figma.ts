/**
 * Figma API 封装 (Electron版本)
 * 通过IPC与主进程通信来处理HTTP请求
 */

// 导入统一的类型定义
import type {
  FigmaNodesResponse,
  FigmaLinkInfo,
  FigmaImagesResponse
} from '../../type';
import * as yaml from 'js-yaml';

/**
 * 解析 Figma 分享链接，提取文件 key 和节点 ID
 * 通过IPC调用主进程中的解析函数
 */
export async function parseFigmaUrl(url: string): Promise<FigmaLinkInfo> {
  try {
    const result = await window.ipcRenderer.invoke('parse-figma-url', url);

    if (!result.success) {
      throw new Error(result.error);
    }

    return result.data;
  } catch (error) {
    throw new Error(`解析 Figma 链接失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 获取 Figma 文件中指定节点的信息
 * @param fileKey Figma 文件的 key
 * @param nodeIds 节点 ID 数组，格式如 ['46-6', '47-8']
 * @returns Promise<FigmaNodesResponse>
 */
export async function getFigmaNodes(fileKey: string, nodeIds: string[]): Promise<FigmaNodesResponse> {
  const url = `https://api.figma.com/v1/files/${fileKey}/nodes`;

  try {
    const result = await window.ipcRenderer.invoke('figma-api-request', {
      url,
      params: {
        ids: nodeIds.join(',')
      }
    });

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.err) {
      throw new Error(`Figma API 错误: ${result.data.err}`);
    }

    return result.data;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`获取 Figma 节点信息失败: ${error.message}`);
    }
    throw new Error('获取 Figma 节点信息失败: 未知错误');
  }
}

/**
 * 递归查找指定ID的节点
 * @param node 当前节点
 * @param targetId 目标节点ID
 * @returns 找到的节点或null
 */
function findNodeById(node: any, targetId: string): any {
  if (!node || typeof node !== 'object') {
    return null
  }

  // 检查当前节点是否匹配
  if (node.id === targetId) {
    return node
  }

  // 递归搜索子节点
  if (node.children && Array.isArray(node.children)) {
    for (const child of node.children) {
      const found = findNodeById(child, targetId)
      if (found) {
        return found
      }
    }
  }

  return null
}

/**
 * 清理节点数据，删除指定字段
 * @param node 原始节点
 * @returns 清理后的节点
 */
function cleanNodeData(node: any): any {
  if (!node || typeof node !== 'object') {
    return node
  }

  // 创建节点的浅拷贝
  const cleanedNode = { ...node }

  // 删除指定字段
  delete cleanedNode.children
  delete cleanedNode.components
  delete cleanedNode.componentSets

  return cleanedNode
}

/**
 * 根据 Figma 分享链接获取文件信息
 * @param figmaUrl Figma 分享链接
 * @param nodeId 可选的节点ID，如果提供则返回该节点的简化信息
 * @returns Promise<FigmaNodesResponse | any>
 */
export async function getFigmaInfoFromUrl(figmaUrl: string, nodeId?: string): Promise<FigmaNodesResponse | any> {
  // 解析链接
  const linkInfo = await parseFigmaUrl(figmaUrl);

  // 获取完整的文件信息
  try {
    const fileInfoUrl = `https://api.figma.com/v1/files/${linkInfo.fileKey}`;
    const result = await window.ipcRenderer.invoke('figma-api-request', {
      url: fileInfoUrl
    });

    if (!result.success) {
      throw new Error(result.error);
    }

    const fileData = result.data;

    // 如果提供了nodeId参数，查找并返回指定节点的简化信息
    if (nodeId) {
      console.log(`🔍 查找节点ID: ${nodeId}`);
      console.log(`📊 fileData 结构:`, {
        hasNodes: !!fileData.nodes,
        nodesKeys: fileData.nodes ? Object.keys(fileData.nodes) : [],
        hasDocument: !!fileData.document,
        documentChildren: fileData.document && fileData.document.children ? fileData.document.children.length : 0
      });

      let targetNode = null;

      // 首先检查是否直接在 nodes 中（对于完整文件数据）
      if (fileData.nodes && fileData.nodes[nodeId]) {
        console.log(`✅ 在 nodes[${nodeId}] 中找到节点`);
        targetNode = fileData.nodes[nodeId].document;
      } else {
        console.log(`❌ 在 nodes[${nodeId}] 中未找到节点`);

        // 如果不在直接节点中，在所有节点的文档中递归查找
        if (fileData.nodes) {
          console.log(`🔄 开始在 nodes 中递归查找...`);
          for (const nodeKey of Object.keys(fileData.nodes)) {
            const nodeInfo = fileData.nodes[nodeKey];
            if (nodeInfo && nodeInfo.document) {
              console.log(`  检查节点: ${nodeKey}`);
              targetNode = findNodeById(nodeInfo.document, nodeId);
              if (targetNode) {
                console.log(`✅ 在 ${nodeKey} 的子节点中找到目标节点`);
                break;
              }
            }
          }
        }

        // 如果还没找到，尝试在 document.children 中查找（对于其他格式的数据）
        if (!targetNode && fileData.document && fileData.document.children) {
          console.log(`🔄 在 document.children 中查找...`);
          for (const page of fileData.document.children) {
            targetNode = findNodeById(page, nodeId);
            if (targetNode) {
              console.log(`✅ 在 document.children 中找到目标节点`);
              break;
            }
          }
        }
      }

      if (!targetNode) {
        console.error(`❌ 未找到ID为 ${nodeId} 的节点`);
        throw new Error(`未找到ID为 ${nodeId} 的节点`);
      }

      console.log(`✅ 找到节点:`, {
        id: targetNode.id,
        name: targetNode.name,
        type: targetNode.type
      });

      // 返回清理后的节点信息
      return cleanNodeData(targetNode);
    }

    // 如果有链接中的节点 ID，获取指定节点信息
    if (linkInfo.nodeId) {
      return await getFigmaNodes(linkInfo.fileKey, [linkInfo.nodeId]);
    }

    // 返回文件基本信息
    return {
      name: fileData.name || 'Unknown File',
      role: 'viewer',
      lastModified: fileData.lastModified || '',
      editorType: 'figma',
      thumbnailUrl: fileData.thumbnailUrl || '',
      version: fileData.version || '0',
      linkAccess: 'view',
      nodes: {}
    };
  } catch (error) {
    throw new Error(`获取 Figma 文件信息失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 获取 Figma 文件中的所有图片
 * @param fileKey Figma 文件的 key
 * @returns Promise<FigmaImagesResponse>
 */
export async function getFigmaImages(fileKey: string): Promise<FigmaImagesResponse> {
  const url = `https://api.figma.com/v1/files/${fileKey}/images`;

  try {
    const result = await window.ipcRenderer.invoke('figma-api-request', {
      url
    });

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.error) {
      throw new Error(`Figma API 错误: ${result.data.error}`);
    }

    return result.data;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`获取 Figma 图片信息失败: ${error.message}`);
    }
    throw new Error('获取 Figma 图片信息失败: 未知错误');
  }
}

/**
 * 根据 Figma 分享链接获取文件中的所有图片
 * @param figmaUrl Figma 分享链接
 * @returns Promise<FigmaImagesResponse>
 */
export async function getFigmaImagesFromUrl(figmaUrl: string): Promise<FigmaImagesResponse> {
  // 解析链接获取文件 key
  const linkInfo = await parseFigmaUrl(figmaUrl);

  // 获取图片信息
  return await getFigmaImages(linkInfo.fileKey);
}

/**
 * 打印 Figma 文件信息到控制台
 * @param figmaUrl Figma 分享链接
 */
export async function printFigmaInfo(figmaUrl: string): Promise<void> {
  try {
    console.log('🔍 正在解析 Figma 链接...');
    const linkInfo = await parseFigmaUrl(figmaUrl);

    console.log('📋 链接解析结果:');
    console.log(JSON.stringify(linkInfo, null, 2));
    console.log('');

    console.log('📡 正在获取 Figma 文件信息...');
    const figmaInfo = await getFigmaInfoFromUrl(figmaUrl);

    console.log('✅ 获取成功！完整文件信息 JSON:');
    console.log(JSON.stringify(figmaInfo, null, 2));

    console.log('');
    console.log('🎉 Figma 文件信息获取完成！');

  } catch (error) {
    console.error('❌ 获取 Figma 文件信息失败:');
    console.error(`   ${error instanceof Error ? error.message : '未知错误'}`);
    throw error;
  }
}

/**
 * 递归删除节点中的指定字段
 * @param node 节点对象
 * @param fieldsToRemove 要删除的字段数组
 * @returns 清理后的节点对象
 */
function removeFieldsRecursively(node: any, fieldsToRemove: string[]): any {
  if (!node || typeof node !== 'object') {
    return node
  }

  // 创建节点的浅拷贝
  const cleanedNode = { ...node }

  // 删除通用字段
  fieldsToRemove.forEach(field => {
    delete cleanedNode[field]
  })

  // 如果节点类型是VECTOR，删除额外的字段
  if (cleanedNode.type === 'VECTOR') {
    const vectorFieldsToRemove = [
      'blendMode',
      'fills',
      'strokes',
      'strokeWeight',
      'strokeAlign'
    ]

    vectorFieldsToRemove.forEach(field => {
      delete cleanedNode[field]
    })
  }

  // 如果有children，递归处理每个子节点
  if (cleanedNode.children && Array.isArray(cleanedNode.children)) {
    cleanedNode.children = cleanedNode.children.map((child: any) =>
      removeFieldsRecursively(child, fieldsToRemove)
    )
  }

  return cleanedNode
}

/**
 * 可下载节点接口
 */
interface DownloadableNode {
  nodeId: string
  nodeName: string
  nodeType: string
  imageRef?: string
}

/**
 * 简化节点接口
 */
interface SimplifiedNode {
  id: string
  name: string
  type: string
  fills?: string
  children?: SimplifiedNode[]
}

/**
 * 简化设计接口
 */
interface SimplifiedDesign {
  nodes: SimplifiedNode[]
  globalVars: GlobalVars
}

/**
 * 全局变量接口
 */
interface GlobalVars {
  styles: { [key: string]: any }
}

/**
 * 从Figma响应数据中提取可下载的节点
 * @param figmaResponse Figma API 响应数据
 * @returns 可下载节点数组
 */
export function extractDownloadableNodes(figmaResponse: any): DownloadableNode[] {
  // 检查响应数据结构
  if (!figmaResponse || !figmaResponse.nodes) {
    return []
  }

  const downloadableNodes: DownloadableNode[] = []

  // 遍历所有节点
  Object.keys(figmaResponse.nodes).forEach(nodeId => {
    const nodeInfo = figmaResponse.nodes[nodeId]

    if (nodeInfo && nodeInfo.document) {
      // 构建简化设计结构
      const simplifiedDesign: SimplifiedDesign = {
        nodes: [convertToSimplifiedNode(nodeInfo.document)],
        globalVars: {
          styles: nodeInfo.styles || {}
        }
      }

      // 提取可下载节点
      const nodes = extractDownloadableNodesFromDesign(simplifiedDesign)
      downloadableNodes.push(...nodes)
    }
  })

  // 基于nodeId去重
  const uniqueNodes = new Map<string, DownloadableNode>()
  downloadableNodes.forEach(node => uniqueNodes.set(node.nodeId, node))

  return Array.from(uniqueNodes.values())
}

/**
 * 将Figma节点转换为简化节点
 */
function convertToSimplifiedNode(node: any): SimplifiedNode {
  const simplifiedNode: SimplifiedNode = {
    id: node.id,
    name: node.name || '',
    type: node.type || ''
  }

  // 处理填充信息
  if (node.fills && Array.isArray(node.fills)) {
    // 检查是否有图片填充
    const hasImageFill = node.fills.some((fill: any) => fill.type === 'IMAGE')
    if (hasImageFill) {
      simplifiedNode.fills = 'image_fill'
    }
  }

  // 递归处理子节点
  if (node.children && Array.isArray(node.children)) {
    simplifiedNode.children = node.children.map((child: any) => convertToSimplifiedNode(child))
  }

  return simplifiedNode
}

/**
 * 从简化设计中提取可下载的节点
 */
function extractDownloadableNodesFromDesign(design: SimplifiedDesign): DownloadableNode[] {
  const downloadableNodes: DownloadableNode[] = []
  const parentMap = new Map<string, SimplifiedNode>()

  // 构建父子节点关系映射
  design.nodes.forEach(node => buildParentMap(node, parentMap))

  // 遍历并收集可下载的节点
  design.nodes.forEach(node => traverseAndCollectNodes(node, parentMap, design.globalVars, downloadableNodes))

  // 基于nodeId去重
  const uniqueNodes = new Map<string, DownloadableNode>()
  downloadableNodes.forEach(node => uniqueNodes.set(node.nodeId, node))

  return Array.from(uniqueNodes.values())
}

/**
 * 构建父子节点关系映射
 */
function buildParentMap(node: SimplifiedNode, parentMap: Map<string, SimplifiedNode>, parent?: SimplifiedNode) {
  if (parent) {
    parentMap.set(node.id, parent)
  }
  node.children?.forEach(child => buildParentMap(child, parentMap, node))
}

/**
 * 遍历节点并收集可下载的节点
 */
function traverseAndCollectNodes(
  node: SimplifiedNode,
  parentMap: Map<string, SimplifiedNode>,
  globalVars: GlobalVars,
  downloadableNodes: DownloadableNode[]
) {
  // 调试日志：检查包含'P'的节点类型
  if (node.type.includes('P') || node.type.includes("P")) {
    console.log(`Node type contains 'P': ${node.type}`, { nodeId: node.id, nodeName: node.name })
  }

  // 1. 如果是图片，直接添加到下载数组中
  if (hasImageFill(node, globalVars)) {
    addDownloadableNode(node, globalVars, downloadableNodes)
  }
  // 2. 如果是VECTOR，判断父节点逻辑
  else if (node.type === "VECTOR" || node.type === "IMAGE-SVG" || node.type.toUpperCase() === "REGULAR_POLYGON") {
    const parent = parentMap.get(node.id)
    if (parent && shouldDownloadParentForVector(parent, globalVars)) {
      addDownloadableNode(parent, globalVars, downloadableNodes)
    } else {
      addDownloadableNode(node, globalVars, downloadableNodes)
    }
  }

  // 递归遍历子节点
  node.children?.forEach(child => traverseAndCollectNodes(child, parentMap, globalVars, downloadableNodes))
}

/**
 * 检查是否应该为VECTOR子节点下载其父节点
 * 判断父节点的子节点中，没有图片节点，没有文本节点
 */
function shouldDownloadParentForVector(parent: SimplifiedNode, globalVars: GlobalVars): boolean {
  if (!parent.children || parent.children.length === 0) {
    return false
  }
  return !parent.children.some(child => hasImageFill(child, globalVars) || isTextNode(child))
}

/**
 * 检查节点是否有图片填充
 */
function hasImageFill(node: SimplifiedNode, globalVars: GlobalVars): boolean {
  if (node.fills) {
    const fills = globalVars.styles[node.fills]
    if (Array.isArray(fills)) {
      return fills.some((fill: any) => fill.type === "IMAGE" && fill.imageRef)
    }
  }

  // 简化版本：直接检查fills标记
  return node.fills === 'image_fill'
}

/**
 * 检查节点是否为文本节点
 */
function isTextNode(node: SimplifiedNode): boolean {
  return node.type === "TEXT"
}

/**
 * 添加可下载节点到数组
 */
function addDownloadableNode(node: SimplifiedNode, globalVars: GlobalVars, downloadableNodes: DownloadableNode[]) {
  const downloadableNode: DownloadableNode = {
    nodeId: node.id,
    nodeName: node.name,
    nodeType: node.type
  }

  // 如果有图片引用，添加imageRef
  if (node.fills && globalVars.styles[node.fills]) {
    const fills = globalVars.styles[node.fills]
    if (Array.isArray(fills)) {
      const imageFill = fills.find((fill: any) => fill.type === "IMAGE" && fill.imageRef)
      if (imageFill) {
        downloadableNode.imageRef = imageFill.imageRef
      }
    }
  }

  downloadableNodes.push(downloadableNode)
}

/**
 * 递归提取节点中的 id、type 和 children 字段
 * @param node 节点对象
 * @param iconNodes 图标节点数组，用于判断哪些节点需要被当做图标处理
 * @returns 只包含 id、type 和 children 的节点对象
 */
function extractBasicFields(node: any, iconNodes: DownloadableNode[] = []): any {
  if (!node || typeof node !== 'object') {
    return node
  }

  // 检查当前节点是否在图标节点数组中
  const isIconNode = iconNodes.some(iconNode => iconNode.nodeId === node.id)

  // 创建只包含基本字段的新对象
  const basicNode: any = {
    id: node.id,
    type: isIconNode ? 'GROUP' : node.type  // 如果是图标节点，类型改为GROUP
  }

  // 如果不是图标节点且有children，递归处理每个子节点
  if (!isIconNode && node.children && Array.isArray(node.children)) {
    basicNode.children = node.children.map((child: any) => extractBasicFields(child, iconNodes))
  }

  return basicNode
}

/**
 * 虚拟DOM节点接口
 */
interface VirtualNode {
  id: string
  tag: string
  children?: VirtualNode[]
}

/**
 * 将 VirtualNode 转换为 HTML 字符串
 * @param node VirtualNode 节点
 * @returns HTML 字符串
 */
function convertToHtml(node: VirtualNode): string {
  const className = sanitizeClassName(node.id)
  const openTag = `<${node.tag} class="${className}">`
  const closeTag = `</${node.tag}>`

  // 如果有子节点，递归转换
  if (node.children && node.children.length > 0) {
    const childrenHtml = node.children.map(child => convertToHtml(child)).join('')
    return `${openTag}${childrenHtml}${closeTag}`
  }

  // 没有子节点的情况
  return `${openTag}${closeTag}`
}

/**
 * 替换 id 中非法的 class 字符
 * @param id 节点ID
 * @returns 清理后的class名称
 */
function sanitizeClassName(id: string): string {
  return id.replace(/[:;]/g, '_')
}

/**
 * 将基本节点转换为虚拟DOM节点
 * @param node 基本节点
 * @returns 虚拟DOM节点
 */
function convertToVirtualNode(node: any): VirtualNode {
  const virtualNode: VirtualNode = {
    id: node.id,
    tag: node.type === 'TEXT' ? 'p' : 'div'
  }

  // 如果有children，递归转换
  if (node.children && Array.isArray(node.children)) {
    virtualNode.children = node.children.map((child: any) => convertToVirtualNode(child))
  }

  return virtualNode
}

/**
 * 从 figma 风格的 JSON 提取根节点
 * @param figmaResponse Figma API 响应数据
 * @returns 虚拟DOM节点
 */
function extractRootNode(figmaResponse: any): VirtualNode | null {
  if (!figmaResponse || !figmaResponse.nodes) {
    return null
  }

  const nodes = figmaResponse.nodes
  const firstKey = Object.keys(nodes)[0]

  if (!nodes[firstKey] || !nodes[firstKey].document) {
    return null
  }

  return nodes[firstKey].document
}

/**
 * 从Figma数据中提取最外层节点的宽度
 * @param figmaResponse Figma API 响应数据
 * @returns 设计稿宽度，默认393
 */
export function extractDesignWidth(figmaResponse: any): number {
  try {
    // 检查响应数据结构
    if (!figmaResponse || !figmaResponse.nodes) {
      return 393 // 默认宽度
    }

    // 获取第一个节点
    const nodes = figmaResponse.nodes
    const firstKey = Object.keys(nodes)[0]

    if (!nodes[firstKey] || !nodes[firstKey].document) {
      return 393
    }

    const rootNode = nodes[firstKey].document

    // 尝试从absoluteBoundingBox获取宽度
    if (rootNode.absoluteBoundingBox && rootNode.absoluteBoundingBox.width) {
      const width = Math.round(rootNode.absoluteBoundingBox.width)
      console.log(`📐 从Figma获取设计稿宽度: ${width}px`)
      return width
    }

    // 如果没有absoluteBoundingBox，尝试从子节点获取
    if (rootNode.children && rootNode.children.length > 0) {
      const firstChild = rootNode.children[0]
      if (firstChild.absoluteBoundingBox && firstChild.absoluteBoundingBox.width) {
        const width = Math.round(firstChild.absoluteBoundingBox.width)
        console.log(`📐 从子节点获取设计稿宽度: ${width}px`)
        return width
      }
    }

    console.log('📐 未找到宽度信息，使用默认宽度: 393px')
    return 393
  } catch (error) {
    console.warn('⚠️ 提取设计稿宽度失败，使用默认值:', error)
    return 393
  }
}

/**
 * 将 Figma JSON 数据格式化成DOM结构的HTML字符串
 * @param figmaResponse Figma API 响应数据
 * @returns 处理后的HTML字符串
 */
export function formatFigmaToBasicDom(figmaResponse: any): string {
  // 检查响应数据结构
  if (!figmaResponse || !figmaResponse.nodes) {
    return ''
  }

  // 先获取图标节点列表
  const iconNodes = extractDownloadableNodes(figmaResponse)

  // 提取根节点
  const rootNode = extractRootNode(figmaResponse)
  if (!rootNode) {
    return ''
  }

  // 提取document中的基本字段，传入图标节点信息
  const basicDocument = extractBasicFields(rootNode, iconNodes)

  // 转换为虚拟DOM节点
  const virtualNode = convertToVirtualNode(basicDocument)

  // 转换为HTML字符串
  return convertToHtml(virtualNode)
}

/**
 * 处理 Figma API 响应数据，不拆分子元素，直接转换为YAML格式
 * 递归删除指定的字段，并转换为紧凑的YAML Flow Style格式以最大化压缩效果
 *
 * 优化策略：
 * 1. 删除所有节点的无用字段：constraints, absoluteRenderBounds, effects, interactions, scrollBehavior
 * 2. 删除VECTOR节点的额外字段：blendMode, fills, strokes, strokeWeight, strokeAlign
 * 3. 使用YAML Flow Style格式，预计压缩率45-55%
 *
 * @param figmaResponse Figma API 响应数据
 * @returns 处理后的YAML字符串，包含完整的document结构和所有子元素
 */
export function processFigmaDataToYaml(figmaResponse: any): string {
  // 要删除的字段列表
  const fieldsToRemove = ['constraints', 'absoluteRenderBounds', 'effects', 'interactions', 'scrollBehavior']

  // 检查响应数据结构
  if (!figmaResponse || !figmaResponse.nodes) {
    return ''
  }

  // 处理所有节点
  const processedNodes: any = {}

  Object.keys(figmaResponse.nodes).forEach(nodeId => {
    const nodeInfo = figmaResponse.nodes[nodeId]

    if (nodeInfo && nodeInfo.document) {
      // 清理整个document结构中的指定字段
      const cleanedDocument = removeFieldsRecursively(nodeInfo.document, fieldsToRemove)

      processedNodes[nodeId] = {
        document: cleanedDocument,
        components: nodeInfo.components || {},
        componentSets: nodeInfo.componentSets || {},
        schemaVersion: nodeInfo.schemaVersion,
        styles: nodeInfo.styles || {}
      }
    }
  })

  // 构建完整的响应结构
  const processedResponse = {
    name: figmaResponse.name,
    lastModified: figmaResponse.lastModified,
    thumbnailUrl: figmaResponse.thumbnailUrl,
    version: figmaResponse.version,
    role: figmaResponse.role,
    editorType: figmaResponse.editorType,
    linkAccess: figmaResponse.linkAccess,
    nodes: processedNodes
  }

  // 转换为YAML格式 (使用Flow Style获得最佳压缩)
  const yamlString = yaml.dump(processedResponse, {
    flowLevel: 0,     // 所有层级都使用flow style (最紧凑)
    lineWidth: -1,    // 不限制行宽
    noRefs: true,     // 不使用引用
    skipInvalid: true, // 跳过无效值
    quotingType: '"', // 使用双引号
    forceQuotes: false // 不强制所有字符串都加引号
  })

  return yamlString
}

/**
 * 处理 Figma API 响应数据，将包含多个子元素的document节点拆分成多个只包含单个子元素的document节点
 * 同时递归删除指定的字段，并转换为紧凑的YAML Flow Style格式以最大化压缩效果
 *
 * 优化策略：
 * 1. 删除所有节点的无用字段：constraints, absoluteRenderBounds, effects, interactions, scrollBehavior
 * 2. 删除VECTOR节点的额外字段：blendMode, fills, strokes, strokeWeight, strokeAlign
 * 3. 使用YAML Flow Style格式，预计压缩率45-55%
 *
 * @param figmaResponse Figma API 响应数据
 * @returns 处理后的YAML字符串数组，每个元素只包含document结构和一个子元素
 */
export function processFigmaData(figmaResponse: any): string[] {
  const result: string[] = []

  // 要删除的字段列表
  const fieldsToRemove = ['constraints', 'absoluteRenderBounds', 'effects', 'interactions', 'scrollBehavior']

  // 检查响应数据结构
  if (!figmaResponse || !figmaResponse.nodes) {
    return result
  }

  // 遍历所有节点
  Object.keys(figmaResponse.nodes).forEach(nodeId => {
    const nodeInfo = figmaResponse.nodes[nodeId]

    // 检查是否有document和children
    if (nodeInfo && nodeInfo.document && nodeInfo.document.children && Array.isArray(nodeInfo.document.children)) {
      const document = nodeInfo.document
      const children = document.children

      // 为每个子元素创建一个新的document结构，只包含document部分
      children.forEach((child: any) => {
        // 先清理子节点中的指定字段
        const cleanedChild = removeFieldsRecursively(child, fieldsToRemove)

        // 清理document本身的指定字段
        const cleanedDocument = removeFieldsRecursively(document, fieldsToRemove)

        const newDocumentStructure = {
          document: {
            ...cleanedDocument, // 保持原有的document属性（已清理）
            children: [cleanedChild] // 只包含当前子元素（已清理）
          }
        }

        // 转换为YAML格式并添加到结果数组 (使用Flow Style获得最佳压缩)
        const yamlString = yaml.dump(newDocumentStructure, {
          flowLevel: 0,     // 所有层级都使用flow style (最紧凑)
          lineWidth: -1,    // 不限制行宽
          noRefs: true,     // 不使用引用
          skipInvalid: true, // 跳过无效值
          quotingType: '"', // 使用双引号
          forceQuotes: false // 不强制所有字符串都加引号
        })
        result.push(yamlString)
      })
    } else {
      // 如果没有children或children不是数组，只返回document结构
      if (nodeInfo && nodeInfo.document) {
        // 清理document中的指定字段
        const cleanedDocument = removeFieldsRecursively(nodeInfo.document, fieldsToRemove)

        const documentStructure = {
          document: cleanedDocument
        }

        // 转换为YAML格式并添加到结果数组 (使用Flow Style获得最佳压缩)
        const yamlString = yaml.dump(documentStructure, {
          flowLevel: 0,     // 所有层级都使用flow style (最紧凑)
          lineWidth: -1,    // 不限制行宽
          noRefs: true,     // 不使用引用
          skipInvalid: true, // 跳过无效值
          quotingType: '"', // 使用双引号
          forceQuotes: false // 不强制所有字符串都加引号
        })
        result.push(yamlString)
      }
    }
  })

  return result
}

/**
 * 递归遍历节点，查找所有图片节点的ID
 * @param node 要遍历的节点
 * @param imageNodeIds 用于收集图片节点ID的数组
 */
function collectImageNodeIds(node: any, imageNodeIds: string[]): void {
  if (!node || typeof node !== 'object') {
    return;
  }

  // 判断当前节点是否是图片节点
  const isImageNode =
    // 方式1: 节点类型是IMAGE
    node.type === 'IMAGE' ||
    // 方式2: 节点的fills属性数组中有type为"IMAGE"的项
    (node.fills && Array.isArray(node.fills) &&
     node.fills.some((fill: any) => fill && fill.type === 'IMAGE'));

  // 如果是图片节点，添加到结果数组
  if (isImageNode && node.id) {
    imageNodeIds.push(node.id);
  }

  // 递归遍历子节点
  if (node.children && Array.isArray(node.children)) {
    node.children.forEach((child: any) => {
      collectImageNodeIds(child, imageNodeIds);
    });
  }
}

/**
 * 获取Figma链接中所有图片节点的ID
 * @param figmaUrl Figma分享链接
 * @returns Promise<string> 返回用逗号分隔的图片节点ID字符串
 */
export async function getImageNodeIdsFromUrl(figmaUrl: string): Promise<string> {
  try {
    // 解析链接获取文件key
    const linkInfo = await parseFigmaUrl(figmaUrl);

    // 获取完整的文件信息（包含所有节点）
    const fileInfoUrl = `https://api.figma.com/v1/files/${linkInfo.fileKey}`;
    const result = await window.ipcRenderer.invoke('figma-api-request', {
      url: fileInfoUrl
    });

    if (!result.success) {
      throw new Error(result.error);
    }

    const fileData = result.data;
    const imageNodeIds: string[] = [];

    // 遍历文件中的所有页面和节点
    if (fileData.document && fileData.document.children) {
      fileData.document.children.forEach((page: any) => {
        collectImageNodeIds(page, imageNodeIds);
      });
    }

    // 返回用逗号分隔的ID字符串
    return imageNodeIds.join(',');

  } catch (error) {
    throw new Error(`获取图片节点ID失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 根据节点ID列表获取对应的图片地址
 * @param fileKey Figma文件的key
 * @param nodeIds 节点ID数组，格式如 ['64:192', '12:21']
 * @param scale 图片缩放比例，默认为2
 * @param useAbsoluteBounds 是否使用绝对边界，默认为true
 * @returns Promise<{[nodeId: string]: string}> 返回节点ID到图片URL的映射对象
 */
export async function getImageUrlsByNodeIds(
  fileKey: string,
  nodeIds: string[],
  scale: number = 2,
  useAbsoluteBounds: boolean = true
): Promise<{[nodeId: string]: string}> {
  if (!nodeIds || nodeIds.length === 0) {
    return {};
  }

  const url = `https://api.figma.com/v1/images/${fileKey}`;

  try {
    const result = await window.ipcRenderer.invoke('figma-api-request', {
      url,
      params: {
        ids: nodeIds.join(','),
        scale: scale.toString(),
        use_absolute_bounds: useAbsoluteBounds.toString()
      }
    });

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.err) {
      throw new Error(`Figma API 错误: ${result.data.err}`);
    }

    // 返回图片URL映射对象
    return result.data.images || {};

  } catch (error) {
    throw new Error(`获取图片URL失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 根据Figma链接和节点ID字符串获取对应的图片地址
 * @param figmaUrl Figma分享链接
 * @param nodeIds 节点ID字符串，格式如 'id1,id2,id3'
 * @param scale 图片缩放比例，默认为2
 * @param useAbsoluteBounds 是否使用绝对边界，默认为true
 * @returns Promise<{[nodeId: string]: string}> 返回节点ID到图片URL的映射对象
 */
export async function getImageUrlsFromUrl(
  figmaUrl: string,
  nodeIds: string,
  scale: number = 2,
  useAbsoluteBounds: boolean = true
): Promise<{[nodeId: string]: string}> {
  try {
    // 解析链接获取文件key
    const linkInfo = await parseFigmaUrl(figmaUrl);

    // 将逗号分隔的字符串转换为数组
    const nodeIdArray = nodeIds.split(',').filter(id => id.trim().length > 0);

    // 调用基础方法获取图片URL
    return await getImageUrlsByNodeIds(linkInfo.fileKey, nodeIdArray, scale, useAbsoluteBounds);

  } catch (error) {
    throw new Error(`从URL获取图片地址失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 下载图片到指定路径
 * @param imageUrls 图片URL映射对象，格式如 {"64:192": "https://..."}
 * @param downloadPath 下载路径
 * @returns Promise<string[]> 返回下载成功的文件路径数组
 */
export async function downloadImages(
  imageUrls: {[nodeId: string]: string},
  downloadPath: string
): Promise<string[]> {
  try {
    const result = await window.ipcRenderer.invoke('download-images', {
      imageUrls,
      downloadPath
    });

    if (!result.success) {
      throw new Error(result.error);
    }

    return result.data.filePaths || [];

  } catch (error) {
    throw new Error(`下载图片失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 初始化Figma数据并自动下载图片到assets文件夹
 * @param figmaUrl Figma分享链接
 * @param assetsPath assets文件夹路径
 * @returns Promise<InitializationResult> 返回初始化结果
 */
export interface InitializationResult {
  figmaInfo: any;
  downloadableNodes: DownloadableNode[];
  downloadedImages: {
    imageUrls: {[nodeId: string]: string};
    downloadedFiles: string[];
    summary: {
      totalImages: number;
      downloadedCount: number;
      downloadPath: string;
    };
  } | null;
  error?: string;
}

export async function initializeFigmaWithImages(
  figmaUrl: string,
  assetsPath: string
): Promise<InitializationResult> {
  try {
    console.log('🚀 开始初始化Figma数据并下载图片...');
    console.log(`🔗 Figma链接: ${figmaUrl}`);
    console.log(`📁 Assets路径: ${assetsPath}`);

    // 1. 获取Figma文件信息
    console.log('📋 正在获取Figma文件信息...');
    const figmaInfo = await getFigmaInfoFromUrl(figmaUrl);
    console.log('✅ Figma文件信息获取成功');

    // 2. 提取可下载节点
    console.log('🔍 正在提取可下载节点...');
    const downloadableNodes = extractDownloadableNodes(figmaInfo);
    console.log(`✅ 提取到 ${downloadableNodes.length} 个可下载节点`);

    // 3. 如果有可下载节点，则直接使用这些节点下载图片
    let downloadedImages: InitializationResult['downloadedImages'] = null;

    if (downloadableNodes.length > 0) {
      try {
        // 解析链接获取文件key
        const linkInfo = await parseFigmaUrl(figmaUrl);

        // 从可下载节点中提取节点ID
        const nodeIds = downloadableNodes.map(node => node.nodeId);
        console.log(`📸 从可下载节点中提取到 ${nodeIds.length} 个节点ID: ${nodeIds.join(', ')}`);

        // 直接使用节点ID获取图片URL
        console.log('🔗 正在获取图片URL...');
        const imageUrls = await getImageUrlsByNodeIds(linkInfo.fileKey, nodeIds, 2, true);
        console.log(`✅ 获取到 ${Object.keys(imageUrls).length} 个图片URL`);

        if (Object.keys(imageUrls).length > 0) {
          console.log(`💾 开始下载图片到: ${assetsPath}`);

          // 下载图片
          const filePaths = await downloadImages(imageUrls, assetsPath);

          downloadedImages = {
            imageUrls,
            downloadedFiles: filePaths,
            summary: {
              totalImages: Object.keys(imageUrls).length,
              downloadedCount: filePaths.length,
              downloadPath: assetsPath
            }
          };

          console.log(`✅ 图片下载完成! 成功下载 ${filePaths.length} 个图片`);
        } else {
          console.log('ℹ️ 未获取到图片URL，跳过图片下载');
        }
      } catch (imageError) {
        console.warn('⚠️ 图片下载过程中出现错误:', imageError);
        // 图片下载失败不影响整体初始化流程
      }
    } else {
      console.log('ℹ️ 没有可下载的节点，跳过图片下载');
    }

    const result: InitializationResult = {
      figmaInfo,
      downloadableNodes,
      downloadedImages
    };

    console.log('🎉 Figma数据初始化完成!');
    return result;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    console.error('❌ Figma数据初始化失败:', errorMessage);

    return {
      figmaInfo: null,
      downloadableNodes: [],
      downloadedImages: null,
      error: errorMessage
    };
  }
}

// 导出所有功能
export default {
  parseFigmaUrl,
  getFigmaNodes,
  getFigmaInfoFromUrl,
  getFigmaImages,
  getFigmaImagesFromUrl,
  printFigmaInfo,
  processFigmaData,
  processFigmaDataToYaml,
  formatFigmaToBasicDom,
  extractDesignWidth,
  extractDownloadableNodes,
  getImageNodeIdsFromUrl,
  getImageUrlsByNodeIds,
  getImageUrlsFromUrl,
  downloadImages,
  initializeFigmaWithImages
};
